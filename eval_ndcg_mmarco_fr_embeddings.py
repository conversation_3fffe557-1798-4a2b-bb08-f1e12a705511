
import numpy as np

from datasets import load_dataset

from sentence_transformers import SentenceTransformer, util
from tqdm import tqdm


# https://huggingface.co/spaces/french-datasets/french-datasets

def dcg_at_k(relevances, k=10):
    relevances = np.asfarray(relevances)[:k]
    if relevances.size:
        return np.sum((2**relevances - 1) / np.log2(np.arange(2, relevances.size + 2)))
    return 0.0


def ndcg_at_k(relevances, k=10):
    dcg = dcg_at_k(relevances, k)
    ideal_dcg = dcg_at_k(sorted(relevances, reverse=True), k)
    return dcg / ideal_dcg if ideal_dcg > 0 else 0.0



if __name__ == "__main__":
    print("=== Évaluation nDCG@10 sur MMARCO (FR) ===")

    print("Chargement du Dataset MMARCO (FR)...")
    # https://huggingface.co/datasets/unicamp-dl/mmarco
    # name = french, collection-french, queries-french, runs-french
    ds_fr = load_dataset("unicamp-dl/mmarco", "french", streaming=True, trust_remote_code=True)
subset_ds_fr = ds_fr['train'].take(200)
    ds_fr = load_dataset("unicamp-dl/mmarco", "french", trust_remote_code=True)
    subset_ds_fr = ds_fr['train'].take(200) # type: ignore

    print("Chargement du modèle d'embeddings...")
    # https://huggingface.co/intfloat/multilingual-e5-base
    model = SentenceTransformer("intfloat/multilingual-e5-base")
    # https://huggingface.co/google/mt5-large
    # google/mt5-large
    # https://huggingface.co/sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
    # paraphrase-multilingual-mpnet-base-v2
    # https://huggingface.co/sentence-transformers/paraphrase-multilingual-mpnet-base-v2
    # BAAI/bge-multilingual-gemma2
    # https://huggingface.co/BAAI/bge-multilingual-gemma2

    scores = []
    for sample in tqdm(subset_ds_fr, desc="Évaluation"):
        query = sample["text"]

"""
scores = []
for sample in tqdm(subset, desc="Évaluation"):
    query = sample["query"]

    # Documents candidats = positifs + négatifs
    pos_docs = [p["passage_text"] for p in sample["positive_passages"]]
    neg_docs = [n["passage_text"] for n in sample["negative_passages"]]
    all_docs = pos_docs + neg_docs

    # Encoder query + docs
    q_emb = model.encode(query, convert_to_tensor=True)
    d_embs = model.encode(all_docs, convert_to_tensor=True)

    # Similarité cosinus
    scores_docs = util.cos_sim(q_emb, d_embs)[0].cpu().numpy()

    # Tri décroissant
    ranked_indices = np.argsort(-scores_docs)
    ranked_docs = [all_docs[i] for i in ranked_indices]

    # Pertinence (1 si positif, 0 sinon)
    res_relevances = [1 if d in pos_docs else 0 for d in ranked_docs]

    # nDCG@10
    scores.append(ndcg_at_k(res_relevances, k=10))

print(f"\nScore moyen nDCG@10 sur 200 queries FR (embeddings) : {np.mean(scores):.4f}")

"""
