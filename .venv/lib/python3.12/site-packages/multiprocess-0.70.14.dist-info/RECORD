_multiprocess/__init__.py,sha256=zuJ1_0yr-gCp0oAe5-vLNCp1myHAXuKVc5MRLv9lzLA,31
_multiprocess/__pycache__/__init__.cpython-312.pyc,,
multiprocess-0.70.14.dist-info/COPYING,sha256=n3_yfLkw0sMgLuB-PS1hRvTeZ20GmjPaMWbJjNuoOpU,1493
multiprocess-0.70.14.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
multiprocess-0.70.14.dist-info/LICENSE,sha256=4BQ3FT_vZOttHzl7wFurBVYxZDD4_8Kq20MzYaZo1KQ,1934
multiprocess-0.70.14.dist-info/METADATA,sha256=M9rwpJwYFIglA4KEK_CMRpotD91YkJsy4TKdaaBrhpY,6573
multiprocess-0.70.14.dist-info/RECORD,,
multiprocess-0.70.14.dist-info/WHEEL,sha256=KxatxaZA14OswIJTdImHhiM2tdZgU-xLZEzs-sYveVc,94
multiprocess-0.70.14.dist-info/top_level.txt,sha256=qtJc8GNdvi6suNpISX0Myln9AXJBYrNuas1MCqRPPqg,27
multiprocess/__info__.py,sha256=M5hUkWt7tceSa9DJcTKXcG439qDshdh7irbfrRZmuic,7475
multiprocess/__init__.py,sha256=XWUBDGorUkDW04h64xe51pUV9N5gzvSDj3tNT2ekifw,1856
multiprocess/__pycache__/__info__.cpython-312.pyc,,
multiprocess/__pycache__/__init__.cpython-312.pyc,,
multiprocess/__pycache__/connection.cpython-312.pyc,,
multiprocess/__pycache__/context.cpython-312.pyc,,
multiprocess/__pycache__/forkserver.cpython-312.pyc,,
multiprocess/__pycache__/heap.cpython-312.pyc,,
multiprocess/__pycache__/managers.cpython-312.pyc,,
multiprocess/__pycache__/pool.cpython-312.pyc,,
multiprocess/__pycache__/popen_fork.cpython-312.pyc,,
multiprocess/__pycache__/popen_forkserver.cpython-312.pyc,,
multiprocess/__pycache__/popen_spawn_posix.cpython-312.pyc,,
multiprocess/__pycache__/popen_spawn_win32.cpython-312.pyc,,
multiprocess/__pycache__/process.cpython-312.pyc,,
multiprocess/__pycache__/queues.cpython-312.pyc,,
multiprocess/__pycache__/reduction.cpython-312.pyc,,
multiprocess/__pycache__/resource_sharer.cpython-312.pyc,,
multiprocess/__pycache__/resource_tracker.cpython-312.pyc,,
multiprocess/__pycache__/shared_memory.cpython-312.pyc,,
multiprocess/__pycache__/sharedctypes.cpython-312.pyc,,
multiprocess/__pycache__/spawn.cpython-312.pyc,,
multiprocess/__pycache__/synchronize.cpython-312.pyc,,
multiprocess/__pycache__/util.cpython-312.pyc,,
multiprocess/connection.py,sha256=GhvxnMNj7sk8Jb0S90gfTx8SHK9YKbquiNFmTZ6uBA4,32182
multiprocess/context.py,sha256=2fYvgfnu3B8wj8UyNndHUHgeuVDoVxlkFFKryycstaU,11610
multiprocess/dummy/__init__.py,sha256=kSekDqD_NCy0FDg7XnxZSgW-Ldg1_iRr07sNwDajKpA,3061
multiprocess/dummy/__pycache__/__init__.cpython-312.pyc,,
multiprocess/dummy/__pycache__/connection.cpython-312.pyc,,
multiprocess/dummy/connection.py,sha256=1j3Rl5_enBM-_kMO6HDmum3kPAoFE4Zs485HV5H-V6s,1598
multiprocess/forkserver.py,sha256=hiltKfLImDYJyAcezNAgMDaQznB2LtYWgwre0QroLRg,12138
multiprocess/heap.py,sha256=9rt5u5m5rkhJNfDWiCLpYDoWIt0LbElmx52yMqk7phQ,11626
multiprocess/managers.py,sha256=Y5m_aCdLE4mSCuyVrwMWg5Nh9f4OdSHDlSajyOgyGao,47562
multiprocess/pool.py,sha256=FTmtfoqkuN8Dd48f5TgdkokoxYN75xcnR78Hw-bLSng,32759
multiprocess/popen_fork.py,sha256=Nvq5vVId24UfkOQxXhxZbcXuo8d6YMc409yRXAamTd0,2374
multiprocess/popen_forkserver.py,sha256=SrEbV8Wv0Uu_UegkaW-cayXRdjTGXr560Yyy90pj-yE,2227
multiprocess/popen_spawn_posix.py,sha256=l7XSWqR5UWiUSJh35qeSElLuNfUeEYwvH5HzKRnnyqg,2029
multiprocess/popen_spawn_win32.py,sha256=A9uvlPmhO8JBzNcEU_Gmix2Q_qYJW1NXZgXPwtN5Ao0,4011
multiprocess/process.py,sha256=ml9spJ-3hFJni-10-bCUkcaIVx-fCe25d1iTMpQ1ZRM,12089
multiprocess/queues.py,sha256=sgXCXnIOVrPScqI3lwRD9t3IshqIBMEksLtouPH9Nzc,12139
multiprocess/reduction.py,sha256=NQQ6KbDhmuAyaDeWaIarTZQokGPhcFda1poNnPm5uNc,9637
multiprocess/resource_sharer.py,sha256=nEApLhMQqd8KunfaNKl3n8vdeiCGPxKrSL1Ja0nNAEk,5132
multiprocess/resource_tracker.py,sha256=_D2iX4IWRe3dOwLoLjfCnXNbDAM4pRzA8qEMTcRfutw,9056
multiprocess/shared_memory.py,sha256=tNJt-EYNyQh6xdRsOpAUl15CqmDxC5ypdBZovZ-lqJQ,18357
multiprocess/sharedctypes.py,sha256=d-9SKRJHRlJJC331IxEoWOUXIeY9zxCbhWejXOmzGw0,6306
multiprocess/spawn.py,sha256=cgtV66HhV_yIVzvdblc8bVdSpem16Ks0BOFu_bV5PDQ,9293
multiprocess/synchronize.py,sha256=6q1ijwWyWLWLO8uUtaYT9MKepAYKfdzWPSEZGyJFP4s,11829
multiprocess/tests/__init__.py,sha256=u1_9_zpFkLmutMON2W5QpRtD5EPbJ9XtcvGCX-ccdw8,198924
multiprocess/tests/__main__.py,sha256=klLr-ieyJvT8cN3l8yxoEM5ll9xvoj_q0tzJd9HfMUo,912
multiprocess/tests/__pycache__/__init__.cpython-312.pyc,,
multiprocess/tests/__pycache__/__main__.cpython-312.pyc,,
multiprocess/tests/__pycache__/mp_fork_bomb.cpython-312.pyc,,
multiprocess/tests/__pycache__/mp_preload.cpython-312.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_fork.cpython-312.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_forkserver.cpython-312.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_main_handling.cpython-312.pyc,,
multiprocess/tests/__pycache__/test_multiprocessing_spawn.cpython-312.pyc,,
multiprocess/tests/mp_fork_bomb.py,sha256=6ADOEzh1aXHZ21aOGoBPhKcgB5sj15G9tQVgSc6GrlY,448
multiprocess/tests/mp_preload.py,sha256=cj2tUiPQQqGhPrXBO9LfaY8l0Dk29UdlHMJdG-7LTpQ,351
multiprocess/tests/test_multiprocessing_fork.py,sha256=BzF6mmub8lAnOGbJF888YrWjKdzcg5TP-v63pckKGqs,479
multiprocess/tests/test_multiprocessing_forkserver.py,sha256=aefqw98Z4nriFWxijdQqJ9x1iK3zN1RW51Dd5NO4XUU,394
multiprocess/tests/test_multiprocessing_main_handling.py,sha256=mtmN0K-spqZCcZVNLf_HrhP186-knpY6eaoFonL1U4U,12018
multiprocess/tests/test_multiprocessing_spawn.py,sha256=jbm4_yI_Dxj3CAl83dwqbNBDwhPyKPtPW65p9KlSGWA,279
multiprocess/util.py,sha256=OPI3CZ34BNwwwa7AqW-eGhnuSUsu-ozy2NRU8BYKuwg,14012
