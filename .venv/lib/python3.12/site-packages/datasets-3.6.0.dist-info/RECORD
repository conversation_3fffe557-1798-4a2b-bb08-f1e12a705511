../../../bin/datasets-cli,sha256=TAwCi4WsOujEySdHu6SMcQZOPgIkp-NG8gdUIBBhrnQ,228
datasets-3.6.0.dist-info/AUTHORS,sha256=L0FBY23tCNHLmvsOKAbumHn8WZZIK98sH53JYxhAchU,327
datasets-3.6.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
datasets-3.6.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
datasets-3.6.0.dist-info/METADATA,sha256=rNyefEqsG6-V8EhpqdNl9Nyt1aVOinIwePAuQUoIk3Q,19747
datasets-3.6.0.dist-info/RECORD,,
datasets-3.6.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets-3.6.0.dist-info/WHEEL,sha256=In9FTNxeP60KnTkGw7wk6mJPYd_dQSjEZmXdBdMCI-8,91
datasets-3.6.0.dist-info/entry_points.txt,sha256=iM-h4A7OQCrZqr3L2mwiyMtPeFj8w4HAHzmI45y3tg0,69
datasets-3.6.0.dist-info/top_level.txt,sha256=9A857YvCQm_Dg3UjeKkWPz9sDBos0t3zN2pf5krTemQ,9
datasets/__init__.py,sha256=2r9PYx352IPBoMHGM3yF1UyV4DpE-ZPKXr3HRbI7_RI,1606
datasets/__pycache__/__init__.cpython-312.pyc,,
datasets/__pycache__/arrow_dataset.cpython-312.pyc,,
datasets/__pycache__/arrow_reader.cpython-312.pyc,,
datasets/__pycache__/arrow_writer.cpython-312.pyc,,
datasets/__pycache__/builder.cpython-312.pyc,,
datasets/__pycache__/combine.cpython-312.pyc,,
datasets/__pycache__/config.cpython-312.pyc,,
datasets/__pycache__/data_files.cpython-312.pyc,,
datasets/__pycache__/dataset_dict.cpython-312.pyc,,
datasets/__pycache__/distributed.cpython-312.pyc,,
datasets/__pycache__/exceptions.cpython-312.pyc,,
datasets/__pycache__/fingerprint.cpython-312.pyc,,
datasets/__pycache__/hub.cpython-312.pyc,,
datasets/__pycache__/info.cpython-312.pyc,,
datasets/__pycache__/inspect.cpython-312.pyc,,
datasets/__pycache__/iterable_dataset.cpython-312.pyc,,
datasets/__pycache__/keyhash.cpython-312.pyc,,
datasets/__pycache__/load.cpython-312.pyc,,
datasets/__pycache__/naming.cpython-312.pyc,,
datasets/__pycache__/search.cpython-312.pyc,,
datasets/__pycache__/splits.cpython-312.pyc,,
datasets/__pycache__/streaming.cpython-312.pyc,,
datasets/__pycache__/table.cpython-312.pyc,,
datasets/arrow_dataset.py,sha256=04xoW_fARh2Nsgz7Rk78r7vhHwBO14tPWKzhfCu6xgo,301860
datasets/arrow_reader.py,sha256=byEDpH_SwzjbnVqW0pXjBaE1NoXFE4Cmwoz7zTB_IvA,25131
datasets/arrow_writer.py,sha256=rwshYb2ClpaCc7xdFhHGaw__p9FkZHupGgQNzCdFBwY,30645
datasets/builder.py,sha256=XlMZRiXRkghHgjWZNDdmPeHGi9rv3EFeG0crfOjgGG0,90575
datasets/combine.py,sha256=iylOVTWReGk_9x1HYFEMkRvB8kLlQq6E0zEHdawp3ds,10892
datasets/commands/__init__.py,sha256=rujbQtxJbwHhF9WQqp2DD9tfVTghDMJdl0v6H551Pcs,312
datasets/commands/__pycache__/__init__.cpython-312.pyc,,
datasets/commands/__pycache__/convert.cpython-312.pyc,,
datasets/commands/__pycache__/convert_to_parquet.cpython-312.pyc,,
datasets/commands/__pycache__/datasets_cli.cpython-312.pyc,,
datasets/commands/__pycache__/delete_from_hub.cpython-312.pyc,,
datasets/commands/__pycache__/env.cpython-312.pyc,,
datasets/commands/__pycache__/test.cpython-312.pyc,,
datasets/commands/convert.py,sha256=yK0fjlM47kzo0W8LTdnEIfcMjDm9UOg2a3FZPA9dvmw,7878
datasets/commands/convert_to_parquet.py,sha256=cCCug82MPSUiA_TUlJLFUhqGdaKNOL2NVpKQNtTvaCQ,1593
datasets/commands/datasets_cli.py,sha256=KHQa0rn3w4DQ_mM-1BaYvsrG6todZFyoBxM3nWjUBZA,1422
datasets/commands/delete_from_hub.py,sha256=o0wdolb1r1Jnl6F0KdqKn3u0l8VR2od6KzbRoqrSNPM,1396
datasets/commands/env.py,sha256=8qg-hpXSXXsHvtYFvJkn5rn9IncqPsjjx3nR8no4a2I,1239
datasets/commands/test.py,sha256=RglIF7uamhDN5AZEgvF-Ur2d_BZFABr2-88IwqcXJ3o,9115
datasets/config.py,sha256=_po0FbqBqy8wSNrsLcjWAmOD8tzEdIYdTMpqkP4VtkI,10306
datasets/data_files.py,sha256=-rpEc0Z4bln1qdcvKdPG7MQctZNrPkEy6M3fyMH52uM,31571
datasets/dataset_dict.py,sha256=KW5Zfnl3dfXHJxSQYuF0qJ5rvGHhJWacOFqMYmWQDrY,110071
datasets/distributed.py,sha256=pulXFluRCmo69KeDqblPz32avS6LCHTGycS77XgI2mY,1562
datasets/download/__init__.py,sha256=lbFOtITDaR7PHrhzJ8VfRnpaOT6NYozSxUcLv_GVfTg,281
datasets/download/__pycache__/__init__.cpython-312.pyc,,
datasets/download/__pycache__/download_config.cpython-312.pyc,,
datasets/download/__pycache__/download_manager.cpython-312.pyc,,
datasets/download/__pycache__/streaming_download_manager.cpython-312.pyc,,
datasets/download/download_config.py,sha256=t5qA5qgy2Q1QJiDnpS8CqxO0XxNQ0ftAvOji99-l-Sk,3796
datasets/download/download_manager.py,sha256=44VSuSzIMJoZ-bDa3uF494jio5JmZFMeGAPzuXYRA7Q,12762
datasets/download/streaming_download_manager.py,sha256=qvcoVsXnAGNi2lzKRktck_DJrIx1fQ7xedm881s0IQw,7537
datasets/exceptions.py,sha256=B93GwElhEvlhHPU9GBSY8if27jhRwu875-gL6B2CL6c,4185
datasets/features/__init__.py,sha256=h3i4VatkedCKKMwaOMn5zykUnF4-MCU9hLzuX8AC58M,529
datasets/features/__pycache__/__init__.cpython-312.pyc,,
datasets/features/__pycache__/audio.cpython-312.pyc,,
datasets/features/__pycache__/features.cpython-312.pyc,,
datasets/features/__pycache__/image.cpython-312.pyc,,
datasets/features/__pycache__/pdf.cpython-312.pyc,,
datasets/features/__pycache__/translation.cpython-312.pyc,,
datasets/features/__pycache__/video.cpython-312.pyc,,
datasets/features/audio.py,sha256=kzIESK4J7NLEWH4EBPs0Dq1WCAOfnfsCEQX6LuZdOxo,12209
datasets/features/features.py,sha256=OR58kFbhs_4rezZMczK_Uifm19V2J3treKH4dLNR9no,95134
datasets/features/image.py,sha256=gSK3L3FahbRK-HLuC0VPtWjR-8Zl61Ax3DiYWT5H5ng,15556
datasets/features/pdf.py,sha256=5Q6MQsfHY21CIsUALPLGrkwtLoN5f61wDIofRarxqps,9291
datasets/features/translation.py,sha256=OO5ZPkKSTGpO8VHifHA8ft3Z-X4XZ9PJzk-w32pCxiE,4448
datasets/features/video.py,sha256=ZyVhJt77GH2XUx3m1-g7V3_1bwm-PPpHUm8MElA5kLo,11026
datasets/filesystems/__init__.py,sha256=jBDUQosQqEFIXUDLZwRWaTgNomwL6Fq2qiYPvvxuae0,1523
datasets/filesystems/__pycache__/__init__.cpython-312.pyc,,
datasets/filesystems/__pycache__/compression.cpython-312.pyc,,
datasets/filesystems/compression.py,sha256=2NnuTGzqmH5wk_Vmp9nhuQCAAZ6bzBpCErvrHVOLR4c,4488
datasets/fingerprint.py,sha256=9nIrIMTcsDdvMvhH56Ml_Zv0uXXR1dFvrolZkWxE-Ik,20333
datasets/formatting/__init__.py,sha256=-pM10fSzw4MVj_L3NFWEv2sUyBh4mbnvCkfXgfS6WII,5412
datasets/formatting/__pycache__/__init__.cpython-312.pyc,,
datasets/formatting/__pycache__/formatting.cpython-312.pyc,,
datasets/formatting/__pycache__/jax_formatter.cpython-312.pyc,,
datasets/formatting/__pycache__/np_formatter.cpython-312.pyc,,
datasets/formatting/__pycache__/polars_formatter.cpython-312.pyc,,
datasets/formatting/__pycache__/tf_formatter.cpython-312.pyc,,
datasets/formatting/__pycache__/torch_formatter.cpython-312.pyc,,
datasets/formatting/formatting.py,sha256=DiFh5gPgAD0gKwtZX3ORs9NbGeghsujUYYsqX4hLroc,26466
datasets/formatting/jax_formatter.py,sha256=mdOu5MLz854eWCfHRX9AkcVKhG5AwndXrGiydO7M0Cw,7135
datasets/formatting/np_formatter.py,sha256=tZy_TsVfylZdyXg0qHF0gveej_7m-wn0zNkXem0ua_s,4826
datasets/formatting/polars_formatter.py,sha256=oTm4l30SgGha-Oku42C0dA91Y8f2oifF9aWvi3QITDk,4744
datasets/formatting/tf_formatter.py,sha256=PI4SywSz4buSEKfpTxMLKPYxxCplxtLcJbofNcecFfs,4959
datasets/formatting/torch_formatter.py,sha256=nkXtPZLZ-D2jRh0gEEDkv3HydIlD_qB_dKPH8GhWe7g,5034
datasets/hub.py,sha256=TFBvGkTXceEmaz0FDlP-mDmQLYLLCZ9T6GT0J8Nn3Gw,9380
datasets/info.py,sha256=zeNDp3dva8_hnXbDIDw4bkpLyJr2GLDBW23WEjgoZKg,19689
datasets/inspect.py,sha256=x51_9bZ_-rVT2SYxLLKuOQoFGgvo0Y444lCbfkJpjbk,17143
datasets/io/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/io/__pycache__/__init__.cpython-312.pyc,,
datasets/io/__pycache__/abc.cpython-312.pyc,,
datasets/io/__pycache__/csv.cpython-312.pyc,,
datasets/io/__pycache__/generator.cpython-312.pyc,,
datasets/io/__pycache__/json.cpython-312.pyc,,
datasets/io/__pycache__/parquet.cpython-312.pyc,,
datasets/io/__pycache__/spark.cpython-312.pyc,,
datasets/io/__pycache__/sql.cpython-312.pyc,,
datasets/io/__pycache__/text.cpython-312.pyc,,
datasets/io/abc.py,sha256=LwDMXYs6YkhZuz1JiMK4PDIqgNjv7I8xH3UMUELW2ys,1672
datasets/io/csv.py,sha256=v4zaWehHb9U3njbdhy7wQnb8qO_c_58XOUC9JgBBVwI,5265
datasets/io/generator.py,sha256=sP_5GNozcxXIgDsPVMW_riqCZdInZ0_iFzcY_X1F-Mo,1909
datasets/io/json.py,sha256=vQZT9vhTbKX5Nyob4zQZR1NXWCft7bT5_6_8DD4XZyo,6697
datasets/io/parquet.py,sha256=IxotIfpNHXvJgFzsbT3-CjB1_FfvKpYhNNU1Akxe9bs,4354
datasets/io/spark.py,sha256=VUIODLHgIbiK0CI0UvthQ_gUO0MQDtHUozvw7Dfs8FI,1797
datasets/io/sql.py,sha256=4Zjw7peVEhhzoDtz2VTCFPqt2Tpy4zMB7T7ajb2GVTY,4234
datasets/io/text.py,sha256=bebEzXBSGC40_Gy94j9ZTJ7Hg0IfrV_4pnIUEhQZVig,1975
datasets/iterable_dataset.py,sha256=fF_bBxMHfAXiCoXNtN3UI6IcxBYNqh3hf88_NJp5mGk,159017
datasets/keyhash.py,sha256=4bqtuEHHlof2BBJIydN2s6N7--wJg54DXgsgzbtbNzA,3896
datasets/load.py,sha256=ViQ8cvFnfN-HCh3fxS9LHUfXl2Th4legOgYdKhysdKU,99829
datasets/naming.py,sha256=aqQqYG4QR8YoxJJMAUyVv_oQyudm4WAApsEHvcozpNg,3001
datasets/packaged_modules/__init__.py,sha256=mzJ6XaAZEzcCAIYNyUEBrf52xOwfs9qFljUNbJbhkw8,5212
datasets/packaged_modules/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/arrow/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/arrow/__pycache__/arrow.cpython-312.pyc,,
datasets/packaged_modules/arrow/arrow.py,sha256=lkadNXfBbJMQNDw-tK4B4Y1KJR5G-J6aAn9I9jHiLWY,3494
datasets/packaged_modules/audiofolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/audiofolder/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/audiofolder/__pycache__/audiofolder.cpython-312.pyc,,
datasets/packaged_modules/audiofolder/audiofolder.py,sha256=fKJ03TQ0fAVEDJHBh7olw7iMLrlgq5TNEQyYZfUrgms,1468
datasets/packaged_modules/cache/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/cache/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/cache/__pycache__/cache.cpython-312.pyc,,
datasets/packaged_modules/cache/cache.py,sha256=sjQDBHJUeLU1U9PUK179BHfn8dHNA2RoudCWeIAv8p8,8196
datasets/packaged_modules/csv/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/csv/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/csv/__pycache__/csv.cpython-312.pyc,,
datasets/packaged_modules/csv/csv.py,sha256=4LShCsr9o4YY0C-n4V37L01u2_2qithYrswSp1WMsRU,8568
datasets/packaged_modules/folder_based_builder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/folder_based_builder/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/folder_based_builder/__pycache__/folder_based_builder.cpython-312.pyc,,
datasets/packaged_modules/folder_based_builder/folder_based_builder.py,sha256=5sSSbuKHcRqCww0p0RKxLvUk_8v57sO0LPavA0hIpY4,21074
datasets/packaged_modules/generator/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/generator/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/generator/__pycache__/generator.cpython-312.pyc,,
datasets/packaged_modules/generator/generator.py,sha256=Oke-26QOyDRkGfmIARqSXDqOJW0sIDjboYCwWSHsbdQ,1002
datasets/packaged_modules/imagefolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/imagefolder/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/imagefolder/__pycache__/imagefolder.cpython-312.pyc,,
datasets/packaged_modules/imagefolder/imagefolder.py,sha256=UpMVe8TUyayzHsVSfKN5wiXcc94QdamMvxauI4oFdw4,1956
datasets/packaged_modules/json/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/json/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/json/__pycache__/json.cpython-312.pyc,,
datasets/packaged_modules/json/json.py,sha256=ipf8GieLlsGt5x1rJKr4ViJWg9oTHNp85OKYyPSW2R0,8698
datasets/packaged_modules/pandas/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/pandas/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/pandas/__pycache__/pandas.cpython-312.pyc,,
datasets/packaged_modules/pandas/pandas.py,sha256=eR0B5iGOHZ1owzezYmlvx5U_rWblmlpCt_PdC5Ax59E,2547
datasets/packaged_modules/parquet/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/parquet/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/parquet/__pycache__/parquet.cpython-312.pyc,,
datasets/packaged_modules/parquet/parquet.py,sha256=4P1SU_5Pqxp-nH2Jm_T8YDMof7YU-x6cUklFOl19wpc,5099
datasets/packaged_modules/pdffolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/pdffolder/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/pdffolder/__pycache__/pdffolder.cpython-312.pyc,,
datasets/packaged_modules/pdffolder/pdffolder.py,sha256=bPYBh9-XOr2C-gg_Fl8h-UKhsVQ7VXjBL2FfW8abiGU,565
datasets/packaged_modules/spark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/spark/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/spark/__pycache__/spark.cpython-312.pyc,,
datasets/packaged_modules/spark/spark.py,sha256=UKu4mRB3k0EFb-Ij83eXpzr7VjCYn_TohQconF8Npag,14689
datasets/packaged_modules/sql/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/sql/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/sql/__pycache__/sql.cpython-312.pyc,,
datasets/packaged_modules/sql/sql.py,sha256=0WWm-Xfputk2_QRCVrbKDbZAqZNHxOGdUwfX__4F5E0,4495
datasets/packaged_modules/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/text/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/text/__pycache__/text.cpython-312.pyc,,
datasets/packaged_modules/text/text.py,sha256=VOJVHkmy4Vm53nspW7QboCkPxd1S0M0uEzun5v8rzUE,5516
datasets/packaged_modules/videofolder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/videofolder/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/videofolder/__pycache__/videofolder.cpython-312.pyc,,
datasets/packaged_modules/videofolder/videofolder.py,sha256=HLTMldDZ3WfK8OAbI2wssBuNCP6ucRBpNLpCoJVDL10,807
datasets/packaged_modules/webdataset/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/webdataset/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/webdataset/__pycache__/_tenbin.cpython-312.pyc,,
datasets/packaged_modules/webdataset/__pycache__/webdataset.cpython-312.pyc,,
datasets/packaged_modules/webdataset/_tenbin.py,sha256=oovYsgR2R3eXSn1xSCLG3oTly1szKDP4UOiRp4ORdIk,8533
datasets/packaged_modules/webdataset/webdataset.py,sha256=nqZQeeYiFM2nc7zEGrUmBcn7I8xBiXJLby2dG9hSOKo,10599
datasets/packaged_modules/xml/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/packaged_modules/xml/__pycache__/__init__.cpython-312.pyc,,
datasets/packaged_modules/xml/__pycache__/xml.cpython-312.pyc,,
datasets/packaged_modules/xml/xml.py,sha256=av0HcLQnKl5d1yM0jfBqVhw9EbzqmO_RsHDfa5pkvx4,2822
datasets/parallel/__init__.py,sha256=wiRFK4x67ez2vvmjwM2Sb9R1yFdf38laSarU9y0Bido,76
datasets/parallel/__pycache__/__init__.cpython-312.pyc,,
datasets/parallel/__pycache__/parallel.cpython-312.pyc,,
datasets/parallel/parallel.py,sha256=E-oOQ6zwKrkLFPwZ-3EOcr_aANJDhE-d6QTq7Mp7WvA,4738
datasets/search.py,sha256=ezL8gWjdcvHqwGHoB2T1jnjrbiCES_7ElmBLRL1DLwY,35600
datasets/splits.py,sha256=zZO9vPnbfzfxXQG8LSAQkajXV7TGB2kEwOWrQxPFQbI,23430
datasets/streaming.py,sha256=lOn__P1Tp2Z8jEbjwrdRPs-abNhUqOTY18fXaXQyPtA,6534
datasets/table.py,sha256=DKnuZvgYzDjk70bAf0g1F6gwpLIdmPGqwlKkvcsOgfA,95878
datasets/utils/__init__.py,sha256=PuZtB9YTbRyvdwubnsx-JGdHuMA7p0I0Rmh0E_uxYF0,999
datasets/utils/__pycache__/__init__.cpython-312.pyc,,
datasets/utils/__pycache__/_dataset_viewer.cpython-312.pyc,,
datasets/utils/__pycache__/_dill.cpython-312.pyc,,
datasets/utils/__pycache__/_filelock.cpython-312.pyc,,
datasets/utils/__pycache__/deprecation_utils.cpython-312.pyc,,
datasets/utils/__pycache__/doc_utils.cpython-312.pyc,,
datasets/utils/__pycache__/experimental.cpython-312.pyc,,
datasets/utils/__pycache__/extract.cpython-312.pyc,,
datasets/utils/__pycache__/file_utils.cpython-312.pyc,,
datasets/utils/__pycache__/filelock.cpython-312.pyc,,
datasets/utils/__pycache__/hub.cpython-312.pyc,,
datasets/utils/__pycache__/info_utils.cpython-312.pyc,,
datasets/utils/__pycache__/logging.cpython-312.pyc,,
datasets/utils/__pycache__/metadata.cpython-312.pyc,,
datasets/utils/__pycache__/patching.cpython-312.pyc,,
datasets/utils/__pycache__/py_utils.cpython-312.pyc,,
datasets/utils/__pycache__/sharding.cpython-312.pyc,,
datasets/utils/__pycache__/stratify.cpython-312.pyc,,
datasets/utils/__pycache__/tf_utils.cpython-312.pyc,,
datasets/utils/__pycache__/tqdm.cpython-312.pyc,,
datasets/utils/__pycache__/track.cpython-312.pyc,,
datasets/utils/__pycache__/typing.cpython-312.pyc,,
datasets/utils/__pycache__/version.cpython-312.pyc,,
datasets/utils/_dataset_viewer.py,sha256=SrE1N18S5yCoCx0rAhwaHNDVS9uhxjspA84iNT4TFRw,4397
datasets/utils/_dill.py,sha256=0QphnYT5cKHJEn17Cs_i1XFYazIfJZUr5mm8ehee_bw,17136
datasets/utils/_filelock.py,sha256=iXW3bxsIr5JWNemhKtF_-q_0ysajkUTItzMm8LY9LBY,2355
datasets/utils/deprecation_utils.py,sha256=hTHwlzRs92NfNVudH71LMpW70sjbsP5amebrIgi3A-U,3452
datasets/utils/doc_utils.py,sha256=HoSm0TFaQaCYGfDgNhpBJ4Xc2WQZuOD6dTxLd9D87fs,407
datasets/utils/experimental.py,sha256=JgOjaEY3RWZ--3u0-ry82gLCDUpudfBfl-hWZ46SyS4,1097
datasets/utils/extract.py,sha256=kKMAujtg5FOK91MBXyWl6FFHZStEPn8WkOE7Jmo2Iq4,13021
datasets/utils/file_utils.py,sha256=im6LXdZy1hf9KsHK3RIHnSI_9V7cVuru5UF4BnN6W3M,54287
datasets/utils/filelock.py,sha256=H6C5dQGFCzVKyeDRRY8fZ4YGTEvvNd-MTjpL_sWYb5k,352
datasets/utils/hub.py,sha256=sD9VpJENA3M9_rWFGavUaVV_GsrOBLEKCZjcqtRdJ_s,438
datasets/utils/info_utils.py,sha256=gAzubjnQbE0YTzB3hf3Cipmx5wCBtOje3fPwjYdzVBE,4330
datasets/utils/logging.py,sha256=tk3Oz51sC6fqBAhoU7tRzKJZO8Spz41260W93npnQaA,5382
datasets/utils/metadata.py,sha256=Hrmn8xUoEzwpJKG3Y6tfJt5t7nW1OCxNjfLTlEaxsrI,9367
datasets/utils/patching.py,sha256=iTeb7XG4faLJKNylq55EcZyCndUXU_XBDvOOkuDz_sc,4955
datasets/utils/py_utils.py,sha256=Ql6QN-Lq7nRB_XTCxS_yBHU0ZmzWz9YqWo32rUuLPwU,28088
datasets/utils/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
datasets/utils/resources/__pycache__/__init__.cpython-312.pyc,,
datasets/utils/resources/creators.json,sha256=XtIpMZefgBOdTevRrQTkFiufbgCbp_iyxseyphYQkn0,257
datasets/utils/resources/languages.json,sha256=Z0rQNPsfje8zMi8KdvvwxF4APwwqcskJFUvhNiLAgPM,199138
datasets/utils/resources/multilingualities.json,sha256=02Uc8RtRzfl13l98Y_alZm5HuMYwPzL78B0S5a1X-8c,205
datasets/utils/resources/readme_structure.yaml,sha256=hNf9msoBZw5jfakQrDb0Af8T325TXdcaHsAO2MUcZvY,3877
datasets/utils/resources/size_categories.json,sha256=_5nAP7z8R6t7_GfER81QudFO6Y1tqYu4AWrr4Aot8S8,171
datasets/utils/sharding.py,sha256=VBQ4bRJQijMNDQTgFb1_ddlQ28wAcA0aQp4e-1jFIAk,4215
datasets/utils/stratify.py,sha256=-MVaLmijYhGyKDpnZS9A8SiHekaIyVm84HVyIIQOmfg,4085
datasets/utils/tf_utils.py,sha256=T3OysLGbkO7y-J-o9OVGyn9l-l-A3ruj-24JM_UULm8,24448
datasets/utils/tqdm.py,sha256=44F0g2fBpJwShh1l88PP7Z8kBihFWA_Yee4sjiQSxes,4303
datasets/utils/track.py,sha256=M81CGLn3MyJzHm98CQkbF3_1DG7evQsw-V52_Bp2paI,1838
datasets/utils/typing.py,sha256=G11ytWmwjqVia2IdziRDIWvQ4mLJee-sKzgJfHqU16E,205
datasets/utils/version.py,sha256=Z82cHpjTbQVJyWgnwSU8DsW2G0y-sSbSoOVeQrAds9k,3281
