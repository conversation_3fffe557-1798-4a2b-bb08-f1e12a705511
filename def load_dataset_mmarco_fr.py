import os

from datasets import load_dataset, DatasetDict, Features, Value
from huggingface_hub import snapshot_download


def load_dataset_mmarco_fr():
    # Forcer l’usage de Xet
    os.environ["HF_HUB_ENABLE_XET"] = "1"
    # Activer le transfert optimisé (multi-thread)
    os.environ["HF_XET_HIGH_PERFORMANCE"] = "1"

    print("Chargement du dataset MMARCO (fr)...")

    # Télécharger uniquement les fichiers FR utiles : queries + collection
    local_dir = snapshot_download(
        # https://huggingface.co/datasets/unicamp-dl/mmarco
        repo_id="unicamp-dl/mmarco",
        repo_type="dataset",
        allow_patterns=[
            "data/google/queries/train/french_queries.train.tsv",
            "data/google/queries/dev/french_queries.dev.tsv",
            "data/google/collections/french_collection.tsv",
        ],
    )

    features_queries = Features(
        {
            "id": Value("int64"),
            "text": Value("string"),
        }
    )

    features_collection = Features(
        {
            "id": Value("int64"),
            "text": Value("string"),
        }
    )

    train = load_dataset(
        "csv",
        data_files=f"{local_dir}/data/google/queries/train/french_queries.train.tsv",
        delimiter="\t",
        column_names=["id", "text"],
        features=features_queries,
        split="train",
    )

    dev = load_dataset(
        "csv",
        data_files=f"{local_dir}/data/google/queries/dev/french_queries.dev.tsv",
        delimiter="\t",
        column_names=["id", "text"],
        features=features_queries,
        split="train",
    )

    collection = load_dataset(
        "csv",
        data_files=f"{local_dir}/data/google/collections/french_collection.tsv",
        delimiter="\t",
        column_names=["id", "text"],
        features=features_collection,
        split="train",
    )

    return DatasetDict(
        {"train_queries": train, "dev_queries": dev, "collection": collection}
    )  # type: ignore


